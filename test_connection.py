#!/usr/bin/env python3
"""
Test script to verify BigCartel API connection without SSL issues.
"""

import requests
import json
from dotenv import load_dotenv
import os

def test_bigcartel_connection():
    """Test connection to BigCartel API."""
    load_dotenv()
    
    url = os.getenv('URL', 'https://squindo.bigcartel.com')
    api_url = f"{url}/products.json?page=1&per_page=10"
    
    print("🧪 TESTING BIGCARTEL API CONNECTION")
    print("=" * 50)
    print(f"Store URL: {url}")
    print(f"API URL: {api_url}")
    print()
    
    # Test 1: Direct connection without proxy
    print("📡 Test 1: Direct connection (no proxy)")
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9'
        }
        
        response = requests.get(
            api_url,
            headers=headers,
            timeout=20,
            verify=True
        )
        
        if response.status_code == 200:
            data = response.json()
            products = data if isinstance(data, list) else data.get('products', [])
            print(f"✅ SUCCESS: Found {len(products)} products")
            
            if products:
                print(f"📦 Sample product: {products[0].get('name', 'No name')}")
                print(f"💰 Price: ${products[0].get('price', 0)}")
                print(f"🔗 Permalink: {products[0].get('permalink', 'No permalink')}")
            
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.SSLError as e:
        print(f"🔒 SSL Error: {e}")
        print("Trying without SSL verification...")
        
        # Test 2: Without SSL verification
        try:
            response = requests.get(
                api_url,
                headers=headers,
                timeout=20,
                verify=False
            )
            
            if response.status_code == 200:
                data = response.json()
                products = data if isinstance(data, list) else data.get('products', [])
                print(f"✅ SUCCESS (no SSL verify): Found {len(products)} products")
                return True
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
        except Exception as e2:
            print(f"❌ Connection failed: {e2}")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def main():
    """Main function."""
    success = test_bigcartel_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CONNECTION TEST PASSED!")
        print("Your improved monitor should work correctly now.")
        print("\nTo run your monitor:")
        print("python3 monitor_improved.py")
    else:
        print("❌ CONNECTION TEST FAILED!")
        print("Please check your internet connection and store URL.")
    
    return success

if __name__ == "__main__":
    main()
