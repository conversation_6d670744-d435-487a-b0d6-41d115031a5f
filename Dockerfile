# Enhanced BigCartel Monitor - Production Docker Image
FROM python:3.11-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for SSL and networking
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    && update-ca-certificates

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt && \
    rm -rf /root/.cache/pip

# Copy application files
COPY monitor_improved.py .
COPY tracked_products.json .

# Create non-root user for security
RUN adduser -D -s /bin/sh monitor

# Create data directory for tracking files
RUN mkdir -p /app/data && \
    chown -R monitor:monitor /app

# Switch to non-root user
USER monitor

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('https://httpbin.org/status/200', timeout=5)" || exit 1

# Run the improved monitor
CMD ["python", "monitor_improved.py"]
