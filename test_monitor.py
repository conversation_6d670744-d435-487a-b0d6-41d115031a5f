#!/usr/bin/env python3
"""
Test suite for the improved BigCartel Monitor

This file demonstrates how to test the improved monitor and validates
the improvements made to the codebase.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import os
import tempfile
from monitor_improved import BigCartelMonitor, MonitorConfig, Product, ProductVariant


class TestMonitorConfig(unittest.TestCase):
    """Test the MonitorConfig dataclass."""

    def test_valid_config(self):
        """Test creating a valid configuration."""
        config = MonitorConfig(
            url="https://test.bigcartel.com",
            webhook="https://discord.com/api/webhooks/test"
        )
        self.assertEqual(config.url, "https://test.bigcartel.com")
        self.assertEqual(config.webhook, "https://discord.com/api/webhooks/test")
        self.assertEqual(config.delay, 15.0)

    def test_missing_required_fields(self):
        """Test that missing required fields raise ValueError."""
        with self.assertRaises(ValueError):
            MonitorConfig(url="", webhook="test")

        with self.assertRaises(ValueError):
            MonitorConfig(url="test", webhook="")

    def test_invalid_delay(self):
        """Test that invalid delay raises ValueError."""
        with self.assertRaises(ValueError):
            MonitorConfig(
                url="https://test.bigcartel.com",
                webhook="https://discord.com/api/webhooks/test",
                delay=0.5
            )

    def test_url_normalization(self):
        """Test URL normalization."""
        config = MonitorConfig(
            url="test.bigcartel.com",
            webhook="https://discord.com/api/webhooks/test"
        )
        self.assertEqual(config.url, "https://test.bigcartel.com")


class TestProduct(unittest.TestCase):
    """Test the Product dataclass."""

    def setUp(self):
        """Set up test data."""
        self.variants = [
            ProductVariant(id="1", title="Small", price=10.0, available=True),
            ProductVariant(id="2", title="Large", price=15.0, available=False)
        ]
        self.product = Product(
            id="123",
            title="Test Product",
            handle="test-product",
            price=10.0,
            url="https://test.bigcartel.com/product/test-product",
            image="https://example.com/image.jpg",
            images=["https://example.com/image.jpg"],
            variants=self.variants,
            status="available"
        )

    def test_is_available_true(self):
        """Test is_available returns True when at least one variant is available."""
        self.assertTrue(self.product.is_available())

    def test_is_available_false(self):
        """Test is_available returns False when no variants are available."""
        for variant in self.variants:
            variant.available = False
        self.assertFalse(self.product.is_available())


class TestBigCartelMonitor(unittest.TestCase):
    """Test the BigCartelMonitor class."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.close()

        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            'URL': 'https://test.bigcartel.com',
            'WEBHOOK': 'https://discord.com/api/webhooks/test',
            'DELAY': '10',
            'KEYWORDS': 'test,sample'
        })
        self.env_patcher.start()

        # Create monitor instance
        with patch.object(BigCartelMonitor, 'TRACKED_PRODUCTS_FILE', self.temp_file.name):
            self.monitor = BigCartelMonitor()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_initialization(self):
        """Test monitor initialization."""
        self.assertEqual(self.monitor.config.url, 'https://test.bigcartel.com')
        self.assertEqual(self.monitor.config.webhook, 'https://discord.com/api/webhooks/test')
        self.assertEqual(self.monitor.config.delay, 10.0)
        self.assertEqual(self.monitor.tracked_products, [])

    def test_is_valid_bigcartel_url(self):
        """Test BigCartel URL validation."""
        valid_urls = [
            "https://test.bigcartel.com",
            "http://shop.bigcartel.com"
        ]
        invalid_urls = [
            "https://example.com",
            "not-a-url"
        ]

        self.assertTrue(self.monitor._is_valid_bigcartel_url(valid_urls))
        self.assertFalse(self.monitor._is_valid_bigcartel_url(invalid_urls))
        self.assertFalse(self.monitor._is_valid_bigcartel_url("not-a-list"))

    def test_create_product_from_api_data(self):
        """Test product creation from API data."""
        api_data = {
            'id': 123,
            'name': 'Test Product',
            'permalink': 'test-product',
            'price': 10.0,
            'images': [{'url': 'https://example.com/image.jpg'}],
            'options': [
                {'id': 1, 'name': 'Small', 'price': 10.0, 'sold_out': False},
                {'id': 2, 'name': 'Large', 'price': 15.0, 'sold_out': True}
            ]
        }

        product = self.monitor._create_product_from_api_data(api_data, "https://test.bigcartel.com")

        self.assertEqual(product.id, "123")
        self.assertEqual(product.title, "Test Product")
        self.assertEqual(product.handle, "test-product")
        self.assertEqual(product.price, 10.0)
        self.assertEqual(len(product.variants), 2)
        self.assertTrue(product.variants[0].available)
        self.assertFalse(product.variants[1].available)

    def test_save_and_load_tracked_products(self):
        """Test saving and loading tracked products."""
        test_products = [
            {'id': '1', 'title': 'Product 1', 'price': '10.00', 'status': 'available'},
            {'id': '2', 'title': 'Product 2', 'price': '20.00', 'status': 'sold-out'}
        ]

        self.monitor.tracked_products = test_products
        self.monitor._save_tracked_products()

        # Clear and reload
        self.monitor.tracked_products = []
        self.monitor._load_tracked_products()

        self.assertEqual(len(self.monitor.tracked_products), 2)
        self.assertEqual(self.monitor.tracked_products[0]['title'], 'Product 1')

    def test_has_product_changed_new_product(self):
        """Test change detection for new products."""
        product = Product(
            id="999",
            title="New Product",
            handle="new-product",
            price=25.0,
            url="https://test.bigcartel.com/product/new-product",
            image=None,
            images=[],
            variants=[],
            status="available"
        )

        self.assertTrue(self.monitor._has_product_changed(product))

    def test_has_product_changed_existing_unchanged(self):
        """Test change detection for unchanged existing products."""
        # Add a product to tracked products
        self.monitor.tracked_products = [
            {'id': '123', 'title': 'Test Product', 'price': '10.0', 'status': 'available'}
        ]

        product = Product(
            id="123",
            title="Test Product",
            handle="test-product",
            price=10.0,
            url="https://test.bigcartel.com/product/test-product",
            image=None,
            images=[],
            variants=[],
            status="available"
        )

        self.assertFalse(self.monitor._has_product_changed(product))

    def test_has_product_changed_existing_changed(self):
        """Test change detection for changed existing products."""
        # Add a product to tracked products
        self.monitor.tracked_products = [
            {'id': '123', 'title': 'Test Product', 'price': '10.00', 'status': 'available'}
        ]

        # Create product with changed price
        product = Product(
            id="123",
            title="Test Product",
            handle="test-product",
            price=15.0,  # Changed price
            url="https://test.bigcartel.com/product/test-product",
            image=None,
            images=[],
            variants=[],
            status="available"
        )

        self.assertTrue(self.monitor._has_product_changed(product))

    @patch('requests.Session.post')
    def test_send_discord_webhook(self, mock_post):
        """Test Discord webhook sending."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        product = Product(
            id="123",
            title="Test Product",
            handle="test-product",
            price=10.0,
            url="https://test.bigcartel.com/product/test-product",
            image="https://example.com/image.jpg",
            images=["https://example.com/image.jpg"],
            variants=[ProductVariant(id="1", title="Default", price=10.0, available=True)],
            status="available"
        )

        self.monitor._send_discord_webhook(product, "test.bigcartel.com")

        mock_post.assert_called_once()
        call_args = mock_post.call_args
        self.assertEqual(call_args[0][0], self.monitor.config.webhook)

        # Verify webhook data structure
        webhook_data = json.loads(call_args[1]['data'])
        self.assertEqual(webhook_data['username'], self.monitor.config.username)
        self.assertEqual(len(webhook_data['embeds']), 1)
        self.assertEqual(webhook_data['embeds'][0]['title'], "Test Product")

        # Verify that only Variant ID fields exist (no variant name fields)
        fields = webhook_data['embeds'][0]['fields']
        variant_id_fields = [f for f in fields if f.get('name') == 'Variant ID']
        self.assertTrue(len(variant_id_fields) > 0, "Should have Variant ID fields")

        # Verify no variant name fields (since info is already in title/other fields)
        variant_name_fields = [f for f in fields if f.get('name') == 'Default']
        self.assertEqual(len(variant_name_fields), 0, "Should not have variant name fields")

        # Verify no ATC links in any field values
        for field in fields:
            self.assertNotIn('Add to Cart', field.get('value', ''), "Should not contain ATC links")

        # Verify the main status field exists (should be "In-Stock" or "Sold Out")
        status_fields = [f for f in fields if f.get('name') == 'Status']
        self.assertEqual(len(status_fields), 1, "Should have exactly one main status field")

        # Verify no variant-specific status fields (redundant with main status)
        variant_status_count = 0
        for i, field in enumerate(fields):
            if field.get('name') == 'Status' and i > 2:  # Status fields after the main product info
                variant_status_count += 1
        self.assertEqual(variant_status_count, 0, "Should not have variant-specific status fields")


def run_tests():
    """Run all tests."""
    unittest.main(verbosity=2)


if __name__ == '__main__':
    run_tests()
