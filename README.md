# Enhanced BigCartel Monitor

## Description
Production-ready BigCartel store monitor with optimized Discord webhooks and enhanced reliability.

### Environment Variables

- `name`: (Required) Docker container name.
- `WEBHOOK`: (Required) The webhook URL to send notifications.
- `URL`: (Required) The URL of the Bigcartel store.

- (Optional)
- `KEYWORDS`: Keywords to filter products (comma-separated)
- `USERNAME`: Bot name for webhook
- `PROXY`: Proxy settings (comma-separated list of proxies)
- `AVATAR_URL`: Avatar URL for webhook
- `COLOUR`: Color for webhook (hexadecimal, e.g., 00FF00 for green)
- `STORE_ICON_URL`: URL for the store icon in webhook embeds (Optional)
- `LOCATION`: Location for proxy (e.g., US, CA)
- `DELAY`: Delay between checks (in seconds)

## How to Run

To run the Bigcartel Monitor, use the following Docker command. Make sure to replace `namehere`, `webhookhere`, and `https://sitehere.com` with your actual values.

```sh
# Build the image first
docker build -t bigcartel-monitor:latest .

# Generic template
docker run -dit --restart unless-stopped --log-opt max-size=5m --log-opt max-file=10 --name namehere -e KEYWORDS="" -e WEBHOOK="webhookhere" -e URL="https://sitehere.com" -e DELAY="15" -e USERNAME="Monitor Name" atxbigblue/bigcartel_svc:latest
```

## US Monitors

### Squindo Monitor
```sh
docker run -dit --restart unless-stopped --log-opt max-size=5m --log-opt max-file=10 --name squindo -e KEYWORDS="" -e WEBHOOK="https://ptb.discord.com/api/webhooks/595473033263841285/i78DFApF19jA7ZnqUe8_1Enilx78999z4RwKhKG1S-ru21BxFMg0tkLZsC3M4n2VnG7i" -e URL="https://squindo.bigcartel.com" -e DELAY="15" -e USERNAME="Squindo Monitor" atxbigblue/bigcartel_svc:latest
```

### ihof-met-store webhook
https://discord.com/api/webhooks/1138476210469486703/nSNq37z5UCDYt6IAkqTQ0rM0hL5xuQ8DELXvbMvmU6tJqbotvmIHi5_WhEdcJ5zrReBs

## Management Commands

```sh
# View running containers
docker ps

# View logs in real-time
docker logs -f squindo

# Stop container
docker stop squindo

# Remove container
docker rm squindo

# Restart container
docker restart squindo
```

## Enhanced Features

- ✅ **Improved reliability** - Better SSL/connection handling with automatic fallback
- ✅ **Optimized Discord webhooks** - Clean format without redundant fields or broken ATC links
- ✅ **Enhanced logging** - Professional structured logging with file rotation
- ✅ **Better performance** - Optimized dependencies and resource usage (~50MB image)
- ✅ **Production security** - Non-root user, minimal Alpine Linux base
- ✅ **Auto-recovery** - Exponential backoff retry logic and health monitoring