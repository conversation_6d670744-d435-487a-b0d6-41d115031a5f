# BIGCARTEL_SVC

## Description
Universal Bigcartel Monitor

### Environment Variables

- `name`: (Required) Docker container name.
- `WEBHOOK`: (Required) The webhook URL to send notifications.
- `URL`: (Required) The URL of the Bigcartel store.

- (Optional)
- `KEYWORDS`: Keywords to filter products (comma-separated)
- `USERNAME`: Bot name for webhook
- `PROXY`: Proxy settings (comma-separated list of proxies)
- `AVATAR_URL`: Avatar URL for webhook
- `COLOUR`: Color for webhook (hexadecimal, e.g., 00FF00 for green)
- `STORE_ICON_URL`: URL for the store icon in webhook embeds (Optional)
- `LOCATION`: Location for proxy (e.g., US, CA)
- `DELAY`: Delay between checks (in seconds)

## How to Run

To run the Bigcartel Monitor, use the following Docker command. Make sure to replace `namehere`, `webhookhere`, and `https://sitehere.com` with your actual values.

```sh
docker run -dit --restart unless-stopped --log-opt max-size=5m --log-opt max-file=10  --name namehere -e KEYWORDS="" -e WEBHOOK="webhookhere" -e URL="https://sitehere.com" atxbigblue/bigcartel_svc:latest
```

## US Monitors
docker run -dit --restart unless-stopped --log-opt max-size=5m --log-opt max-file=10  --name squindo -e KEYWORDS="" -e WEBHOOK="https://ptb.discord.com/api/webhooks/595473033263841285/i78DFApF19jA7ZnqUe8_1Enilx78999z4RwKhKG1S-ru21BxFMg0tkLZsC3M4n2VnG7i" -e URL="https://squindo.bigcartel.com" atxbigblue/bigcartel_svc:latest