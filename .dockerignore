# Ignore Python bytecode files
__pycache__/

# Ignore virtual environment directory
venv/

# Ignore Dockerfile and docker-compose.yml
Dockerfile
docker-compose.yml

# Ignore node_modules directory
node_modules/

# Ignore npm debug log
npm-debug.log*

# Ignore any log files
*.log

# Ignore any temporary files
*.tmp

# Ignore any environment files
.env

# Ignore any test files
test/
tests/
*.test.js
*.spec.js

# Ignore any coverage files
coverage/

# Ignore any build directories
build/
dist/

# Ignore any documentation files
docs/

# Ignore any example files
examples/

# Ignore any configuration files
config/
