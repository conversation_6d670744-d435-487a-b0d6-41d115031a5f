#!/usr/bin/env python3
"""
Enhanced BigCartel Product Monitor

A robust, production-ready monitor for BigCartel stores with improved:
- Error handling and resilience
- Code organization and maintainability
- Performance optimization
- Type safety and validation
- Logging and monitoring
"""

import requests
import json
import time
from datetime import datetime, timezone
import urllib3
import logging
import os
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from dotenv import load_dotenv
from random_user_agent.user_agent import UserAgent
from random_user_agent.params import SoftwareName, HardwareType
from fp.fp import FreeProxy
from urllib.parse import quote_plus, urlparse

# Configure logging with better formatting
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('monitor.log', mode='a', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProductVariant:
    """Data class for product variants."""
    id: str
    title: str
    price: float
    available: bool

@dataclass
class Product:
    """Data class for products."""
    id: str
    title: str
    handle: str
    price: float
    url: str
    image: Optional[str]
    images: List[str]
    variants: List[ProductVariant]
    status: str

    def is_available(self) -> bool:
        """Check if any variant is available."""
        return any(variant.available for variant in self.variants)

@dataclass
class MonitorConfig:
    """Configuration data class with validation."""
    url: str
    webhook: str
    delay: float = 15.0
    location: str = 'US'
    keywords: str = ''
    proxy: str = ''
    username: str = 'Monitor'
    avatar_url: str = 'https://media.discordapp.net/attachments/764707485864165386/1314474225683267617/CB_Fish.png'
    colour: str = '00FF00'
    store_icon_url: str = 'https://media.discordapp.net/attachments/764707485864165386/1148998496771395654/store-icon-png-6.png'

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.url:
            raise ValueError("URL is required")
        if not self.webhook:
            raise ValueError("WEBHOOK is required")
        if self.delay < 1:
            raise ValueError("DELAY must be at least 1 second")

        # Validate URL format
        if not self.url.startswith(('http://', 'https://')):
            self.url = f"https://{self.url}"

        # Ensure URL ends with proper format
        if not self.url.endswith('.bigcartel.com'):
            if '.bigcartel.com' not in self.url:
                raise ValueError("URL must be a valid BigCartel store URL")

class BigCartelMonitor:
    """Enhanced BigCartel product monitor with improved error handling and structure."""

    TRACKED_PRODUCTS_FILE = 'tracked_products.json'
    SOFTWARE_NAMES = [SoftwareName.CHROME.value]
    HARDWARE_TYPE = [HardwareType.MOBILE__PHONE]
    MAX_PAGES = 4
    MAX_RETRIES = 3
    REQUEST_TIMEOUT = 20

    def __init__(self):
        """Initialize the monitor with configuration and setup."""
        load_dotenv()

        # Create configuration object with validation
        self.config = MonitorConfig(
            url=os.getenv('URL', ''),
            webhook=os.getenv('WEBHOOK', ''),
            delay=float(os.getenv('DELAY', '15')),
            location=os.getenv('LOCATION', 'US'),
            keywords=os.getenv('KEYWORDS', ''),
            proxy=os.getenv('PROXY', ''),
            username=os.getenv('USERNAME', 'Monitor'),
            avatar_url=os.getenv('AVATAR_URL', 'https://media.discordapp.net/attachments/764707485864165386/1314474225683267617/CB_Fish.png'),
            colour=os.getenv('COLOUR', '00FF00'),
            store_icon_url=os.getenv('STORE_ICON_URL', 'https://media.discordapp.net/attachments/764707485864165386/1148998496771395654/store-icon-png-6.png')
        )

        self.tracked_products: List[Dict] = []
        self.user_agent_rotator = UserAgent(
            software_names=self.SOFTWARE_NAMES,
            hardware_type=self.HARDWARE_TYPE
        )
        self.session = self._create_session()

        logger.info("BigCartel Monitor initialized successfully")

    def _create_session(self) -> requests.Session:
        """Create and configure a requests session."""
        session = requests.Session()
        session.headers.update({
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        })
        return session

    def _load_tracked_products(self) -> None:
        """Load the tracked products list from file if it exists."""
        self.tracked_products = []
        try:
            if os.path.exists(self.TRACKED_PRODUCTS_FILE):
                with open(self.TRACKED_PRODUCTS_FILE, 'r', encoding='utf-8') as f:
                    loaded = json.load(f)
                    if isinstance(loaded, list):
                        self.tracked_products = loaded
                        logger.info(f"Loaded {len(self.tracked_products)} tracked products from previous state")
                    else:
                        logger.warning("Invalid tracked products file format - resetting")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in tracked products file: {e} - resetting")
        except Exception as e:
            logger.error(f"Error loading tracked products file: {e}")

    def _save_tracked_products(self) -> None:
        """Save the current tracked products list to file, ensuring uniqueness."""
        if not self.tracked_products:
            return

        # Remove duplicates while preserving order (keep latest)
        seen_ids: Set[str] = set()
        unique_products = []

        for product_entry in reversed(self.tracked_products):
            product_id = product_entry.get('id')
            if product_id and product_id not in seen_ids:
                unique_products.insert(0, product_entry)
                seen_ids.add(product_id)

        if len(unique_products) != len(self.tracked_products):
            logger.warning(
                f"De-duplicated tracked products. "
                f"Original: {len(self.tracked_products)}, "
                f"Unique: {len(unique_products)}"
            )
            self.tracked_products = unique_products

        try:
            with open(self.TRACKED_PRODUCTS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.tracked_products, f, indent=2, ensure_ascii=False)
            logger.info(f"Tracked products saved to {self.TRACKED_PRODUCTS_FILE}")
        except Exception as e:
            logger.error(f"Error saving tracked products file: {e}")

    def _get_proxy(self, proxy_list: List[str], proxy_no: int) -> Dict[str, str]:
        """Get proxy configuration for requests."""
        if not proxy_list:
            try:
                proxy = FreeProxy(country_id=[self.config.location], rand=True).get()
                if proxy:
                    return {"http": proxy, "https": proxy}
                else:
                    logger.warning("No free proxy found, continuing without proxy")
                    return {}
            except Exception as e:
                logger.error(f"Error getting free proxy: {e}, continuing without proxy")
                return {}
        else:
            proxy_url = f"http://{proxy_list[proxy_no]}"
            return {"http": proxy_url, "https": proxy_url}

    def _is_valid_bigcartel_url(self, urls: List[str]) -> bool:
        """Check if any of the URLs are valid BigCartel URLs."""
        if not isinstance(urls, list):
            return False
        return any('bigcartel.com' in url.lower() for url in urls)

    def _create_product_from_api_data(self, product_data: Dict, base_url: str) -> Product:
        """Create a Product object from BigCartel API data."""
        variants = []
        for option in product_data.get('options', []):
            variants.append(ProductVariant(
                id=str(option.get('id', product_data.get('id'))),
                title=option.get('name', 'Default'),
                price=float(option.get('price', product_data.get('price', 0.0))),
                available=not option.get('sold_out', True)
            ))

        # Ensure at least one default variant if no options
        if not variants:
            variants.append(ProductVariant(
                id=str(product_data.get('id')),
                title='Default',
                price=float(product_data.get('price', 0.0)),
                available=product_data.get('on_sale', False)
            ))

        main_image = None
        images = []
        for img in product_data.get('images', []):
            if img.get('url'):
                images.append(img['url'])
                if main_image is None:
                    main_image = img['url']

        return Product(
            id=str(product_data.get('id')),
            title=product_data.get('name', 'No Title'),
            handle=product_data.get('permalink', 'No Handle'),
            price=float(product_data.get('price', 0.0)),
            url=f"{base_url.replace('/products.json', '')}/product/{product_data.get('permalink', '')}",
            image=main_image or f"https://images.bigcartel.com/product_images/{product_data.get('id')}/product.jpg",
            images=images,
            variants=variants,
            status='available' if any(v.available for v in variants) else 'sold-out'
        )

    def _scrape_site(self, urls: List[str], headers: Dict[str, str], proxy: Dict[str, str]) -> List[Product]:
        """Scrape BigCartel sites and return Product objects."""
        products = []
        logger.info("Starting product scrape...")

        for url in urls:
            page = 1
            products_fetched = 0

            while page <= self.MAX_PAGES:
                try:
                    api_url = f"{url}/products.json?page={page}&per_page=100"
                    logger.info(f"Fetching: {api_url}")

                    # Retry logic with exponential backoff
                    for attempt in range(self.MAX_RETRIES):
                        try:
                            response = self.session.get(
                                api_url,
                                headers=headers,
                                proxies=proxy,
                                verify=False,  # Consider security implications
                                timeout=self.REQUEST_TIMEOUT
                            )
                            response.raise_for_status()
                            break
                        except requests.exceptions.RequestException as e:
                            if attempt == self.MAX_RETRIES - 1:
                                raise
                            logger.warning(f"Request attempt {attempt + 1} failed: {e}")
                            time.sleep(2 ** attempt)  # Exponential backoff

                    try:
                        output = response.json()
                        api_products = output if isinstance(output, list) else output.get('products', [])

                        if not api_products:
                            if page == 1:
                                logger.warning(f"No products found at {url}")
                            break

                        if page == 1:
                            logger.info(f"Products on first page for {url}: {len(api_products)}")

                        num_products_on_page = len(api_products)
                        products_fetched += num_products_on_page
                        logger.info(f"Processing page {page} for {url} ({num_products_on_page} products)")

                        for product_data in api_products:
                            try:
                                product = self._create_product_from_api_data(product_data, url)
                                products.append(product)
                            except Exception as e:
                                logger.error(f"Error processing product {product_data.get('id', 'unknown')}: {e}")
                                continue

                        page += 1

                    except json.JSONDecodeError as e:
                        logger.error(f"JSON decode error for {api_url}: {e}")
                        break

                except requests.exceptions.HTTPError as e:
                    logger.error(f"HTTP error for {api_url}: {e}")
                    if e.response and e.response.status_code == 404:
                        logger.warning(f"Store {url} might not exist or products.json is not available")
                    break

                except requests.exceptions.RequestException as e:
                    logger.error(f'Error scraping page {page} of {url}: {str(e)}')
                    break

            logger.info(f'Finished fetching products for {url} ({products_fetched} total processed)')

        return products

    def _has_product_changed(self, product: Product) -> bool:
        """Check if a product has changed compared to tracked version."""
        existing = next((item for item in self.tracked_products if item['id'] == product.id), None)
        if not existing:
            return True  # New product

        # Check for changes in key attributes
        return (
            existing.get('price') != str(product.price) or
            existing.get('status') != product.status or
            existing.get('title') != product.title
        )

    def _send_discord_webhook(self, product: Product, website_name: str) -> None:
        """Send a webhook to Discord with product details."""
        logger.info(f"Sending webhook for: {product.title}")
        current_time_utc = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')

        embed_data = {
            "username": self.config.username,
            "avatar_url": self.config.avatar_url,
            "embeds": [{
                "title": product.title,
                "url": product.url,
                "color": int(self.config.colour, 16),
                "thumbnail": {"url": product.image} if product.image else None,
                "fields": [],
                "footer": {
                    'text': f"{self.config.username} • {current_time_utc}",
                    'icon_url': self.config.avatar_url
                },
                "author": {
                    "name": website_name,
                    "icon_url": self.config.store_icon_url
                }
            }]
        }

        # Add status and price fields
        status_value = "In-Stock" if product.is_available() else "Sold Out"
        embed_data["embeds"][0]["fields"].extend([
            {"name": "Status", "value": status_value, "inline": False},
            {"name": "Price", "value": f"${product.price:.2f}", "inline": True},
            {"name": "SKU/Handle", "value": product.handle, "inline": True}
        ])

        # Add variant information
        for variant in product.variants:
            variant_status = "Available" if variant.available else "Sold Out"
            atc_link = f"{self.config.url.replace('/products.json', '')}/cart/add_item?item_id={variant.id}"
            embed_data["embeds"][0]["fields"].append({
                "name": f"Variant: {variant.title}",
                "value": f"ID: {variant.id} - Status: {variant_status}\n[Add to Cart]({atc_link})",
                "inline": False
            })

        # Add eBay links
        ebay_title_query = quote_plus(product.title)
        ebay_referral = "&campid=YOUR_CAMPID&toolid=10001"  # Placeholder for actual referral
        ebay_sold_link = f"https://www.ebay.com/sch/i.html?_nkw={ebay_title_query}&LH_Sold=1&LH_Complete=1{ebay_referral}"
        ebay_active_link = f"https://www.ebay.com/sch/i.html?_nkw={ebay_title_query}{ebay_referral}"

        embed_data["embeds"][0]["fields"].extend([
            {"name": "eBay [US]", "value": f"[Sold]({ebay_sold_link}) | [Active]({ebay_active_link})", "inline": True},
            {"name": "eBay [UK]", "value": f"[Sold]({ebay_sold_link.replace('www.ebay.com', 'www.ebay.co.uk')}) | [Active]({ebay_active_link.replace('www.ebay.com', 'www.ebay.co.uk')})", "inline": True},
        ])

        try:
            logger.info(f"Sending webhook to: {self.config.webhook}")
            result = self.session.post(
                self.config.webhook,
                data=json.dumps(embed_data),
                headers={"Content-Type": "application/json"}
            )
            result.raise_for_status()
            logger.info(f"Webhook delivered successfully: {result.status_code} for {product.title}")
            time.sleep(1)  # Rate limiting
        except requests.exceptions.RequestException as e:
            logger.error(f'Failed to send webhook for {product.title}: {str(e)}')
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Webhook response: {e.response.text}")

    def _update_tracked_product(self, product: Product) -> None:
        """Update or add a product to the tracked products list."""
        product_dict = {
            'id': product.id,
            'title': product.title,
            'price': str(product.price),
            'url': product.url,
            'status': product.status,
            'images': product.images,
            'handle': product.handle
        }

        # Find existing product and update or append new
        existing_idx = next((i for i, item in enumerate(self.tracked_products) if item['id'] == product.id), -1)

        if existing_idx == -1:
            self.tracked_products.append(product_dict)
            logger.info(f"Added new product to tracking: {product.title}")
        else:
            self.tracked_products[existing_idx] = product_dict
            logger.info(f"Updated tracked product: {product.title}")

    def _process_products(self, products: List[Product], website_name: str, is_first_run: bool) -> None:
        """Process scraped products and send notifications for changes."""
        keywords = [k.strip().lower() for k in self.config.keywords.split(',') if k.strip()]

        for product in products:
            # Filter by keywords if specified
            if keywords and not any(keyword in product.title.lower() for keyword in keywords):
                continue

            if is_first_run:
                # On first run, just add to tracking without notification
                self._update_tracked_product(product)
            else:
                # Check for changes and notify if necessary
                if self._has_product_changed(product):
                    logger.info(f"Product changed: {product.title}")
                    self._update_tracked_product(product)

                    # Send notification if product is available
                    if product.is_available():
                        self._send_discord_webhook(product, website_name)
                        logger.info(f'Notification sent for product: {product.title}')
                else:
                    logger.debug(f"Product unchanged: {product.title}")

        logger.info(f"Currently tracking {len(self.tracked_products)} products")

    def run(self) -> None:
        """Main function to monitor and send notifications."""
        logger.info('Enhanced BigCartel Monitor starting...')
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # Load previously tracked products
        self._load_tracked_products()
        is_first_run = not self.tracked_products

        if is_first_run:
            logger.info("First run detected - will initialize product tracking without immediate notifications")

        # Validate configuration
        base_url = self.config.url.rstrip('/')
        urls = [base_url]

        if not self._is_valid_bigcartel_url(urls):
            logger.error('Invalid URL format. Please provide a valid BigCartel store URL.')
            return

        # Extract website name for notifications
        parsed_url = urlparse(base_url)
        website_name = parsed_url.netloc if parsed_url.netloc else base_url

        # Setup proxy configuration
        proxy_no = 0
        proxy_list = [p.strip() for p in self.config.proxy.split(',') if p.strip()]

        logger.info("Monitor Configuration:")
        logger.info(f"Store URL: {self.config.url}")
        logger.info(f"Check Delay: {self.config.delay} seconds")
        logger.info(f"Keywords Filter: {self.config.keywords or 'None'}")
        logger.info(f"Proxies configured: {len(proxy_list)}")

        current_proxy = self._get_proxy(proxy_list, proxy_no)
        logger.info(f"Starting with proxy: {current_proxy.get('http', 'No proxy')}")

        # Main monitoring loop
        while True:
            current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logger.info(f"Monitoring for product changes... [{current_time_str}]")

            headers = {'User-Agent': self.user_agent_rotator.get_random_user_agent()}

            try:
                # Scrape products
                products = self._scrape_site(urls, headers, current_proxy)

                if not products and not is_first_run:
                    logger.warning("No products found during scraping")

                # Process products for changes and notifications
                self._process_products(products, website_name, is_first_run)

                if is_first_run and products:
                    logger.info(f"Initialized tracking for {len(self.tracked_products)} products")
                    is_first_run = False

                # Save tracked products
                self._save_tracked_products()

            except Exception as e:
                logger.error('Error during monitoring loop: %s', str(e), exc_info=True)
                logger.info("Attempting to rotate proxy and user-agent")

                # Rotate proxy if available
                if proxy_list:
                    proxy_no = (proxy_no + 1) % len(proxy_list)
                current_proxy = self._get_proxy(proxy_list, proxy_no)
                logger.info(f"Switched to new proxy: {current_proxy.get('http', 'No proxy')}")

            logger.info(f"Waiting for {self.config.delay} seconds before next check")
            time.sleep(self.config.delay)


def main():
    """Main entry point."""
    try:
        monitor = BigCartelMonitor()
        monitor.run()
    except KeyboardInterrupt:
        logger.info("Monitor stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        raise


if __name__ == '__main__':
    main()
