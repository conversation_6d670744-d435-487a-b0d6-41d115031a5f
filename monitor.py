import requests as rq
import json
import time
from datetime import datetime
import urllib3
import logging
import os
from dotenv import load_dotenv
from random_user_agent.user_agent import UserAgent
from random_user_agent.params import SoftwareName, HardwareType
from fp.fp import FreeProxy
from urllib.parse import quote_plus, urlparse

# Initialize logging
logging.basicConfig(
    level=logging.DEBUG,  # Temporarily set to DEBUG for detailed output
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class BigCartelMonitor:
    INSTOCK_FILE = 'instock.txt'
    SOFTWARE_NAMES = [SoftwareName.CHROME.value]
    HARDWARE_TYPE = [HardwareType.MOBILE__PHONE]

    def __init__(self):
        load_dotenv()
        self.config = {
            'URL': os.getenv('URL', ''),
            'WEBHOOK': os.getenv('WEBHOOK', ''),
            'DELAY': float(os.getenv('DELAY', '15')),
            'LOCATION': os.getenv('LOCATION', 'US'),
            'KEYWORDS': os.getenv('KEYWORDS', ''),
            'PROXY': os.getenv('PROXY', ''),
            'USERNAME': os.getenv('USERNAME', 'Monitor'),
            'AVATAR_URL': os.getenv('AVATAR_URL', 'https://media.discordapp.net/attachments/764707485864165386/1314474225683267617/CB_Fish.png?ex=6753e705&is=67529585&hm=93dae1bfb8342460abac2089009ec563dadd5feff735ed8c55885892851e6fb9&=&format=webp&quality=lossless'),
            'COLOUR': os.getenv('COLOUR', '00FF00'),  # Default to green, hex
            'STORE_ICON_URL': os.getenv('STORE_ICON_URL', 'https://media.discordapp.net/attachments/764707485864165386/1148998496771395654/store-icon-png-6.png'),
        }
        self.instock = []
        self.user_agent_rotator = UserAgent(software_names=self.SOFTWARE_NAMES, hardware_type=self.HARDWARE_TYPE)
        self.session = rq.Session() # Use a persistent session

    def _load_instock(self):
        """Load the instock list from file if it exists."""
        self.instock = []
        try:
            if os.path.exists(self.INSTOCK_FILE):
                with open(self.INSTOCK_FILE, 'r') as f:
                    loaded = json.load(f)
                    if isinstance(loaded, list):
                        self.instock = loaded
                        logging.info(f"Loaded {len(self.instock)} items from previous state")
                    else:
                        logging.warning("Invalid instock file format - resetting")
        except json.JSONDecodeError:
            logging.error("Invalid JSON in instock file - resetting")
        except Exception as e:
            logging.error(f"Error loading instock file: {e}")

    def _save_instock(self):
        """Save the current instock list to file, ensuring uniqueness."""
        # ---- START DE-DUPLICATION ----
        if self.instock: # Only process if not empty
            seen_ids = set()
            unique_instock = []
            # Iterate in reverse to keep the latest encountered version of a duplicate ID
            for product_entry in reversed(self.instock):
                if product_entry['id'] not in seen_ids:
                    # Insert at the beginning to maintain original order as much as possible
                    unique_instock.insert(0, product_entry)
                    seen_ids.add(product_entry['id'])
            
            if len(unique_instock) != len(self.instock):
                logging.warning(f"De-duplicated instock list. Original: {len(self.instock)}, Unique: {len(unique_instock)}. Kept latest entries for duplicates.")
                self.instock = unique_instock
        # ---- END DE-DUPLICATION ----

        try:
            with open(self.INSTOCK_FILE, 'w') as f:
                json.dump(self.instock, f, indent=4) # Save the de-duplicated list
            logging.info(f"INSTOCK list saved to {self.INSTOCK_FILE}")
        except Exception as e:
            logging.error(f"Error saving instock file: {e}")

    def _get_proxy(self, proxy_list, proxy_no):
        """Function to handle proxy rotation and fetching new proxies."""
        if not proxy_list:
            try:
                proxy = FreeProxy(country_id=[self.config['LOCATION']], rand=True).get()
                if proxy:
                    return {"http": proxy, "https": proxy} # Return for both http and https
                else:
                    logging.error("No free proxy found, continuing without proxy.")
                    return {}
            except Exception as e:
                logging.error(f"Error getting free proxy: {e}, continuing without proxy.")
                return {}
        else:
            return {"http": f"http://{proxy_list[proxy_no]}", "https": f"http://{proxy_list[proxy_no]}"}


    def _check_bigcartel_url(self, urls):
        """Checks if any of the URLs are valid BigCartel URLs."""
        if not isinstance(urls, list):
            return False
        return any('bigcartel.com' in url.lower() for url in urls)

    def _scrape_site(self, urls, headers, proxy):
        """Scrapes the specified BigCartel sites and adds items to array."""
        items = []
        logging.info("Starting product scrape...")

        for url in urls:
            page = 1
            total_products_for_site = 0 # Renamed for clarity
            products_fetched = 0
            MAX_PAGES = 4

            while True:
                try:
                    api_url = f"{url}/products.json?page={page}&per_page=100"
                    logging.info(f"Fetching: {api_url}")

                    response = self.session.get(
                        api_url,
                        headers=headers,
                        proxies=proxy,
                        verify=False, # Consider security implications
                        timeout=20
                    )
                    response.raise_for_status()

                    try:
                        output = response.json() # Use response.json()
                        products = output if isinstance(output, list) else output.get('products', [])

                        if not products and page == 1: # No products found on the first page
                            logging.warning(f"No products found at {url}. It might be an empty store or an issue with the URL/API.")
                            break

                        if page == 1:
                            total_products_for_site = len(products) # This might not be total if paginated beyond 100
                            logging.info(f"Products on first page for {url}: {len(products)}")


                        num_products_on_page = len(products)
                        products_fetched += num_products_on_page
                        logging.info(f"Processing page {page} for {url} ({num_products_on_page} products)")

                        for product in products:
                            product_name = product.get('name', 'No Title')
                            logging.debug(f"Processing product: {product_name}")

                            main_image = next((img['url'] for img in product.get('images', []) if img.get('url')), None)

                            product_item = {
                                'type': 'product',
                                'title': product_name,
                                'image': main_image or f"https://images.bigcartel.com/product_images/{product.get('id')}/product.jpg",
                                'id': str(product.get('id')),
                                'handle': product.get('permalink', 'No Handle'),
                                'price': f"{product.get('price', 0.0):.2f}",
                                'url': f"{url.replace('/products.json', '')}/product/{product.get('permalink', '')}",
                                'images': [img['url'] for img in product.get('images', []) if img.get('url')],
                                'options': product.get('options', []),
                                'variants': [{
                                    'id': str(opt.get('id', product.get('id'))), # Use option id if available
                                    'title': opt.get('name', 'Default'),
                                    'price': f"{opt.get('price', product.get('price', 0.0)):.2f}", # Use option price
                                    'available': not opt.get('sold_out', True)
                                } for opt in product.get('options', [{'id': product.get('id'), 'name': 'Default', 'price': product.get('price', 0.0), 'sold_out': not product.get('on_sale', False)}])] # Ensure default variant if no options
                            }
                            status_str = "✓ Available" if any(v['available'] for v in product_item['variants']) else "✗ Sold Out"
                            logging.debug(f"Status: {status_str}, Price: ${product_item['price']}")
                            items.append(product_item)

                        # BigCartel API doesn't give total count, so rely on empty product list or MAX_PAGES
                        if not products or page >= MAX_PAGES:
                            logging.info(f'Finished fetching products for {url} ({products_fetched} total processed)')
                            break
                        page += 1

                    except json.JSONDecodeError as e:
                        logging.error(f"JSON decode error while fetching {api_url}: {e}. Response text: {response.text[:200]}")
                        break
                    except rq.exceptions.HTTPError as e:
                        logging.error(f"HTTP error for {api_url}: {e}")
                        if e.response.status_code == 404:
                            logging.warning(f"Store {url} might not exist or products.json is not available.")
                        break # Stop trying for this URL on HTTP error

                except rq.exceptions.RequestException as e:
                    logging.error(f'Error scraping page {page} of {url}: {str(e)}')
                    break # Stop trying for this URL on request exception
        return items

    def _checker(self, product_id, current_data):
        """Determines whether the product status has changed."""
        existing = next((item for item in self.instock if item['id'] == product_id), None)
        if existing:
            # Check for changes in price, status, or title
            return (
                existing.get('price') == current_data.get('price') and
                existing.get('status') == current_data.get('status') and
                existing.get('title') == current_data.get('title')
            )
        return False # New product

    def _discord_webhook(self, product_item, base_url, website_name):
        """Send a webhook to Discord with product details."""
        if product_item.get('initial'):
            logging.info("Sending initial webhook...")
            # Potentially send a startup message here if desired
            return

        logging.info(f"Sending webhook for: {product_item.get('title')}")
        current_time_utc = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

        embed_data = {
            "username": self.config['USERNAME'],
            "avatar_url": self.config['AVATAR_URL'],
            "embeds": [{
                "title": product_item['title'],
                "url": product_item['url'],
                "color": int(self.config['COLOUR'], 16),
                "thumbnail": {"url": product_item['image']},
                "fields": [],
                "footer": {
                    'text': f"{self.config['USERNAME']} • {current_time_utc}",
                    'icon_url': self.config['AVATAR_URL']
                },
                "author": {
                    "name": website_name,
                    "icon_url": self.config['STORE_ICON_URL']
                }
            }]
        }

        is_available = any(v.get('available', False) for v in product_item.get('variants', []))
        status_value = "In-Stock" if is_available else "Sold Out"

        embed_data["embeds"][0]["fields"].append({"name": "Status", "value": status_value, "inline": False})
        embed_data["embeds"][0]["fields"].append({"name": "Price", "value": f"${product_item['price']}", "inline": True})
        embed_data["embeds"][0]["fields"].append({"name": "SKU/Handle", "value": product_item['handle'], "inline": True})


        for variant in product_item.get('variants', []):
            variant_id = variant.get('id', product_item.get('id')) # Fallback to product ID
            variant_title = variant.get('title', 'Default')
            variant_status = "Available" if variant.get('available') else "Sold Out"
            atc_link = f"{base_url.replace('/products.json', '')}/cart/add_item?item_id={variant_id}" # Example ATC, may vary
            embed_data["embeds"][0]["fields"].append({
                "name": f"Variant: {variant_title}",
                "value": f"ID: {variant_id} - Status: {variant_status}\n[Add to Cart]({atc_link})", # Basic ATC
                "inline": False
            })

        ebay_title_query = quote_plus(product_item['title'])
        ebay_referral = "&campid=YOUR_CAMPID&toolid=10001" # Placeholder for actual referral
        eBay_sold_link = f"https://www.ebay.com/sch/i.html?_nkw={ebay_title_query}&LH_Sold=1&LH_Complete=1{ebay_referral}"
        eBay_active_link = f"https://www.ebay.com/sch/i.html?_nkw={ebay_title_query}{ebay_referral}"

        embed_data["embeds"][0]["fields"].extend([
            {"name": "eBay [US]", "value": f"[Sold]({eBay_sold_link}) | [Active]({eBay_active_link})", "inline": True},
            {"name": "eBay [UK]", "value": f"[Sold]({eBay_sold_link.replace('www.ebay.com', 'www.ebay.co.uk')}) | [Active]({eBay_active_link.replace('www.ebay.com', 'www.ebay.co.uk')})", "inline": True},
        ])

        try:
            logging.info(f"Sending webhook to: {self.config['WEBHOOK']}")
            result = self.session.post(self.config['WEBHOOK'], data=json.dumps(embed_data), headers={"Content-Type": "application/json"})
            result.raise_for_status()
            logging.info(f"Webhook delivered successfully: {result.status_code} for {product_item.get('title')}")
            time.sleep(1)
        except rq.exceptions.RequestException as e:
            logging.error(f'Failed to send webhook for {product_item.get("title")}: {str(e)}')
            if hasattr(e, 'response') and e.response is not None:
                logging.error(f"Webhook response: {e.response.text}")


    def _comparitor(self, product, website_name):
        """Compare and send notifications for new or restocked products."""
        if product['type'] != 'product':
            return

        title = product.get('title', 'Unknown Product')
        product_id = product.get('id')

        # ---- START DEBUG PRINTS ----
        if product_id == "30476152": # Only print for your specific test item
            print(f"DEBUG: _comparitor called for product ID: {product_id}, Title: {title}")
        # ---- END DEBUG PRINTS ----

        logging.info(f"Checking availability for: {title} (ID: {product_id})")

        try:
            is_in_stock_now = any(variant.get('available', False) for variant in product.get('variants', []))

            # ---- START DEBUG PRINTS ----
            if product_id == "30476152":
                print(f"DEBUG: For ID {product_id}, is_in_stock_now: {is_in_stock_now}")
            # ---- END DEBUG PRINTS ----

            product_data_for_tracking = {
                'id': product_id,
                'title': title,
                'price': product.get('price'),
                'url': product.get('url'),
                'status': 'available' if is_in_stock_now else 'sold-out',
                'images': product.get('images', []), # Keep all images for potential future use
                'handle': product.get('handle', '')
                # Storing full variant data in self.instock might be too much,
                # but could be useful for detailed change tracking.
                # For now, stick to top-level status.
            }

            existing_idx = next((i for i, item in enumerate(self.instock) if item['id'] == product_id), -1)

            # ---- START DEBUG PRINTS ----
            if product_id == "30476152":
                print(f"DEBUG: For ID {product_id}, existing_idx in self.instock: {existing_idx}")
                if existing_idx == -1:
                    print(f"DEBUG: ID {product_id} is considered NEW by _comparitor.")
                else:
                    print(f"DEBUG: ID {product_id} is considered EXISTING by _comparitor (index: {existing_idx}).")
            # ---- END DEBUG PRINTS ----

            if existing_idx == -1:  # New product
                logging.info(f"New product found: '{title}' (Status: {product_data_for_tracking['status']})")
                print(f"DEBUG_COMPARITOR_ADD: Adding NEW product to self.instock: ID {product_id}, Title: {title}") # ADD THIS LINE
                self.instock.append(product_data_for_tracking)
                # ---- START DEBUG PRINTS ----
                if product_id == "30476152":
                    print(f"DEBUG: For NEW ID {product_id}, attempting to notify if is_in_stock_now ({is_in_stock_now}) is True.")
                # ---- END DEBUG PRINTS ----
                if is_in_stock_now:
                    # ---- START DEBUG PRINTS ----
                    if product_id == "30476152":
                         print(f"DEBUG: ID {product_id} IS IN STOCK (is_in_stock_now={is_in_stock_now}). ATTEMPTING WEBHOOK.")
                    # ---- END DEBUG PRINTS ----
                    self._discord_webhook(product, self.config['URL'], website_name)
                    logging.info(f'Notification sent for new product: {title}')
            else: # Existing product, check for changes
                is_unchanged = self._checker(product_id, product_data_for_tracking)
                old_status = self.instock[existing_idx]['status']
                new_status = product_data_for_tracking['status']

                if not is_unchanged:
                    log_message = f"Product '{title}' details changed."
                    if old_status != new_status:
                        log_message += f" Status: {old_status} -> {new_status}."
                    else: # Price or other details changed
                        log_message += f" Price: {self.instock[existing_idx].get('price')} -> {product_data_for_tracking.get('price')}."
                    logging.info(log_message)

                    self.instock[existing_idx] = product_data_for_tracking
                    if is_in_stock_now : # Notify if it's now in stock, or if it was in stock and details changed
                        self._discord_webhook(product, self.config['URL'], website_name)
                        logging.info(f'Notification sent for updated product: {title}')
                else:
                    logging.debug(f"Product '{title}' unchanged.")


            logging.debug(f"Currently tracking {len(self.instock)} products")

        except Exception as e:
            logging.error(f"Error in _comparitor for {title}: {str(e)}", exc_info=True)


    def run(self):
        """Main function to monitor and send notifications."""
        logging.info('Monitor starting.')
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning) # Moved here

        self._load_instock()
        # ---- START NEW DEBUG PRINT ----
        print(f"DEBUG: After _load_instock(), is ID 30476152 in self.instock? {'Yes' if any(item['id'] == '30476152' for item in self.instock) else 'No'}")
        print(f"DEBUG: Length of self.instock after load: {len(self.instock)}")
        # ---- END NEW DEBUG PRINT ----
        is_first_run = not self.instock # Simpler check

        if is_first_run:
            logging.info("First run detected - will initialize product tracking without immediate notifications for existing items.")

        if not self.config['URL']:
            logging.error('No URL provided. Please set the URL in .env file.')
            return

        base_url = self.config['URL'].rstrip('/')
        urls = [base_url] # Assuming single URL monitoring for now

        if not self._check_bigcartel_url(urls):
            logging.error('Invalid URL format. Please provide a valid BigCartel store URL.')
            return

        parsed_url = urlparse(base_url)
        website_name = parsed_url.netloc if parsed_url.netloc else base_url
        self._discord_webhook({'initial': True, 'title': 'Monitor Started'}, base_url, website_name) # Send a startup notification

        proxy_no = 0
        proxy_list = [p.strip() for p in self.config['PROXY'].split(',') if p.strip()]
        keywords = [k.strip().lower() for k in self.config['KEYWORDS'].split(',') if k.strip()]


        logging.info("Monitor Configuration:")
        logging.info(f"Store URL: {self.config['URL']}")
        logging.info(f"Check Delay: {self.config['DELAY']} seconds")
        logging.info(f"Keywords Filter: {self.config['KEYWORDS'] or 'None'}")
        logging.info(f"Proxies configured: {len(proxy_list)}")


        current_proxy = self._get_proxy(proxy_list, proxy_no)
        logging.info(f"Starting with proxy: {current_proxy.get('http', 'No proxy')} at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        while True:
            current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logging.info(f"Monitoring for new products... [{current_time_str}]")
            headers = {'User-Agent': self.user_agent_rotator.get_random_user_agent()}

            try:
                items = self._scrape_site(urls, headers, current_proxy)
                if items is None: # Indicates a critical error in scraping
                    logging.error("Scraping returned None, attempting proxy rotation.")
                    raise Exception("Scraping failed critically")


                for item in items: # 5th indent level (20 spaces)
                    title_lower = item['title'].lower()
                    if not keywords or any(key in title_lower for key in keywords): # 6th indent level (24 spaces)
                        if is_first_run: # 7th indent level (28 spaces)
                            # On first run, just add to instock without notification
                            is_in_stock_now = any(variant.get('available', False) for variant in item.get('variants', []))
                            self.instock.append({
                                'id': item['id'],
                                'title': item['title'],
                                'price': item['price'],
                                'url': item['url'],
                                'status': 'available' if is_in_stock_now else 'sold-out',
                                'images': item.get('images', []),
                                'handle': item['handle']
                            })
                        else: # This else corresponds to `if is_first_run:` (7th indent level - 28 spaces)
                            # ---- START NEW DEBUG PRINT ----
                            if item.get('id') == "30476152": # 8th indent level (32 spaces)
                                print(f"DEBUG RUN_LOOP: Before _comparitor for ID 30476152.")
                                print(f"DEBUG RUN_LOOP: is_first_run is: {is_first_run}")
                                print(f"DEBUG RUN_LOOP: Is ID 30476152 in self.instock NOW? {'Yes' if any(i['id'] == '30476152' for i in self.instock) else 'No'}")
                                if any(i['id'] == '30476152' for i in self.instock):
                                    found_at = next((idx for idx, i_s in enumerate(self.instock) if i_s['id'] == '30476152'), -99)
                                    print(f"DEBUG RUN_LOOP: If yes, found at index: {found_at}")
                            # ---- END NEW DEBUG PRINT ----
                            self._comparitor(item, website_name) # 8th indent level (32 spaces)

                if is_first_run and items: # 5th indent level (20 spaces)
                    logging.info(f"Initialized tracking for {len(self.instock)} products.")
                    is_first_run = False # Mark first run as complete

                self._save_instock() # 5th indent level (20 spaces)

            except Exception as e: # 4th indent level (16 spaces)
                logging.error('Error during monitoring loop: %s', str(e), exc_info=True)
                logging.info("Attempting to rotate proxy and user-agent.")

                if proxy_list: # Only rotate if proxies are configured
                    proxy_no = (proxy_no + 1) % len(proxy_list)
                current_proxy = self._get_proxy(proxy_list, proxy_no) # Will fetch free if proxy_list is empty or exhausted
                logging.info(f"Switched to new proxy: {current_proxy.get('http', 'No proxy')} at {current_time_str}")
            
            logging.info(f"Waiting for {self.config['DELAY']} seconds before next check.")
            time.sleep(self.config['DELAY'])

if __name__ == '__main__':
    monitor_instance = BigCartelMonitor()
    monitor_instance.run()
