import requests as rq
import json
import time
from datetime import datetime
import urllib3
import logging
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from dotenv import load_dotenv
from random_user_agent.user_agent import UserAgent
from random_user_agent.params import SoftwareName, HardwareType
from fp.fp import FreeProxy
from urllib.parse import quote_plus, urlparse

# Configure logging with better formatting
logging.basicConfig(
    level=logging.INFO,  # Changed from DEBUG to INFO for production
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('monitor.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProductVariant:
    """Data class for product variants."""
    id: str
    title: str
    price: float
    available: bool

@dataclass
class Product:
    """Data class for products."""
    id: str
    title: str
    handle: str
    price: float
    url: str
    image: Optional[str]
    images: List[str]
    variants: List[ProductVariant]
    status: str

    def is_available(self) -> bool:
        """Check if any variant is available."""
        return any(variant.available for variant in self.variants)

@dataclass
class MonitorConfig:
    """Configuration data class."""
    url: str
    webhook: str
    delay: float = 15.0
    location: str = 'US'
    keywords: str = ''
    proxy: str = ''
    username: str = 'Monitor'
    avatar_url: str = 'https://media.discordapp.net/attachments/764707485864165386/1314474225683267617/CB_Fish.png'
    colour: str = '00FF00'
    store_icon_url: str = 'https://media.discordapp.net/attachments/764707485864165386/1148998496771395654/store-icon-png-6.png'

    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.url:
            raise ValueError("URL is required")
        if not self.webhook:
            raise ValueError("WEBHOOK is required")
        if self.delay < 1:
            raise ValueError("DELAY must be at least 1 second")

class BigCartelMonitor:
    """Enhanced BigCartel product monitor with improved error handling and structure."""

    INSTOCK_FILE = 'instock.txt'
    SOFTWARE_NAMES = [SoftwareName.CHROME.value]
    HARDWARE_TYPE = [HardwareType.MOBILE__PHONE]
    MAX_PAGES = 4
    MAX_RETRIES = 3
    REQUEST_TIMEOUT = 20

    def __init__(self):
        """Initialize the monitor with configuration and setup."""
        load_dotenv()

        # Create configuration object with validation
        self.config = MonitorConfig(
            url=os.getenv('URL', ''),
            webhook=os.getenv('WEBHOOK', ''),
            delay=float(os.getenv('DELAY', '15')),
            location=os.getenv('LOCATION', 'US'),
            keywords=os.getenv('KEYWORDS', ''),
            proxy=os.getenv('PROXY', ''),
            username=os.getenv('USERNAME', 'Monitor'),
            avatar_url=os.getenv('AVATAR_URL', 'https://media.discordapp.net/attachments/764707485864165386/1314474225683267617/CB_Fish.png'),
            colour=os.getenv('COLOUR', '00FF00'),
            store_icon_url=os.getenv('STORE_ICON_URL', 'https://media.discordapp.net/attachments/764707485864165386/1148998496771395654/store-icon-png-6.png')
        )

        self.tracked_products: List[Dict] = []
        self.user_agent_rotator = UserAgent(
            software_names=self.SOFTWARE_NAMES,
            hardware_type=self.HARDWARE_TYPE
        )
        self.session = self._create_session()

        logger.info("BigCartel Monitor initialized successfully")

    def _create_session(self) -> rq.Session:
        """Create and configure a requests session."""
        session = rq.Session()
        session.headers.update({
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        })
        return session

    def _load_tracked_products(self) -> None:
        """Load the tracked products list from file if it exists."""
        self.tracked_products = []
        try:
            if os.path.exists(self.INSTOCK_FILE):
                with open(self.INSTOCK_FILE, 'r', encoding='utf-8') as f:
                    loaded = json.load(f)
                    if isinstance(loaded, list):
                        self.tracked_products = loaded
                        logger.info(f"Loaded {len(self.tracked_products)} tracked products from previous state")
                    else:
                        logger.warning("Invalid tracked products file format - resetting")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in tracked products file: {e} - resetting")
        except Exception as e:
            logger.error(f"Error loading tracked products file: {e}")

    def _save_tracked_products(self) -> None:
        """Save the current tracked products list to file, ensuring uniqueness."""
        if not self.tracked_products:
            return

        # Remove duplicates while preserving order (keep latest)
        seen_ids = set()
        unique_products = []

        for product_entry in reversed(self.tracked_products):
            product_id = product_entry.get('id')
            if product_id and product_id not in seen_ids:
                unique_products.insert(0, product_entry)
                seen_ids.add(product_id)

        if len(unique_products) != len(self.tracked_products):
            logger.warning(
                f"De-duplicated tracked products. "
                f"Original: {len(self.tracked_products)}, "
                f"Unique: {len(unique_products)}"
            )
            self.tracked_products = unique_products

        try:
            with open(self.INSTOCK_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.tracked_products, f, indent=2, ensure_ascii=False)
            logger.info(f"Tracked products saved to {self.INSTOCK_FILE}")
        except Exception as e:
            logger.error(f"Error saving tracked products file: {e}")

    def _get_proxy(self, proxy_list: List[str], proxy_no: int) -> Dict[str, str]:
        """Get proxy configuration for requests."""
        if not proxy_list:
            try:
                proxy = FreeProxy(country_id=[self.config.location], rand=True).get()
                if proxy:
                    return {"http": proxy, "https": proxy}
                else:
                    logger.warning("No free proxy found, continuing without proxy")
                    return {}
            except Exception as e:
                logger.error(f"Error getting free proxy: {e}, continuing without proxy")
                return {}
        else:
            proxy_url = f"http://{proxy_list[proxy_no]}"
            return {"http": proxy_url, "https": proxy_url}

    def _is_valid_bigcartel_url(self, urls: List[str]) -> bool:
        """Check if any of the URLs are valid BigCartel URLs."""
        if not isinstance(urls, list):
            return False
        return any('bigcartel.com' in url.lower() for url in urls)

    def _create_product_from_api_data(self, product_data: Dict, base_url: str) -> Product:
        """Create a Product object from BigCartel API data."""
        variants = []
        for option in product_data.get('options', []):
            variants.append(ProductVariant(
                id=str(option.get('id', product_data.get('id'))),
                title=option.get('name', 'Default'),
                price=float(option.get('price', product_data.get('price', 0.0))),
                available=not option.get('sold_out', True)
            ))

        # Ensure at least one default variant if no options
        if not variants:
            variants.append(ProductVariant(
                id=str(product_data.get('id')),
                title='Default',
                price=float(product_data.get('price', 0.0)),
                available=product_data.get('on_sale', False)
            ))

        main_image = None
        images = []
        for img in product_data.get('images', []):
            if img.get('url'):
                images.append(img['url'])
                if main_image is None:
                    main_image = img['url']

        return Product(
            id=str(product_data.get('id')),
            title=product_data.get('name', 'No Title'),
            handle=product_data.get('permalink', 'No Handle'),
            price=float(product_data.get('price', 0.0)),
            url=f"{base_url.replace('/products.json', '')}/product/{product_data.get('permalink', '')}",
            image=main_image or f"https://images.bigcartel.com/product_images/{product_data.get('id')}/product.jpg",
            images=images,
            variants=variants,
            status='available' if any(v.available for v in variants) else 'sold-out'
        )

    def _scrape_site(self, urls: List[str], headers: Dict[str, str], proxy: Dict[str, str]) -> List[Dict]:
        """Scrape BigCartel sites and return product data."""
        items = []
        logger.info("Starting product scrape...")

        for url in urls:
            page = 1
            products_fetched = 0

            while page <= self.MAX_PAGES:
                try:
                    api_url = f"{url}/products.json?page={page}&per_page=100"
                    logger.info(f"Fetching: {api_url}")

                    for attempt in range(self.MAX_RETRIES):
                        try:
                            response = self.session.get(
                                api_url,
                                headers=headers,
                                proxies=proxy,
                                verify=False,  # Consider security implications
                                timeout=self.REQUEST_TIMEOUT
                            )
                            response.raise_for_status()
                            break
                        except rq.exceptions.RequestException as e:
                            if attempt == self.MAX_RETRIES - 1:
                                raise
                            logger.warning(f"Request attempt {attempt + 1} failed: {e}")
                            time.sleep(2 ** attempt)  # Exponential backoff

                    try:
                        output = response.json()
                        products = output if isinstance(output, list) else output.get('products', [])

                        if not products:
                            if page == 1:
                                logger.warning(f"No products found at {url}")
                            break

                        if page == 1:
                            logger.info(f"Products on first page for {url}: {len(products)}")

                        num_products_on_page = len(products)
                        products_fetched += num_products_on_page
                        logger.info(f"Processing page {page} for {url} ({num_products_on_page} products)")

                        for product in products:
                            try:
                                # Create structured product data
                                product_item = {
                                    'type': 'product',
                                    'title': product.get('name', 'No Title'),
                                    'id': str(product.get('id')),
                                    'handle': product.get('permalink', 'No Handle'),
                                    'price': f"{product.get('price', 0.0):.2f}",
                                    'url': f"{url.replace('/products.json', '')}/product/{product.get('permalink', '')}",
                                    'images': [img['url'] for img in product.get('images', []) if img.get('url')],
                                    'variants': []
                                }

                                # Set main image
                                product_item['image'] = (
                                    product_item['images'][0] if product_item['images']
                                    else f"https://images.bigcartel.com/product_images/{product.get('id')}/product.jpg"
                                )

                                # Process variants
                                for opt in product.get('options', []):
                                    product_item['variants'].append({
                                        'id': str(opt.get('id', product.get('id'))),
                                        'title': opt.get('name', 'Default'),
                                        'price': f"{opt.get('price', product.get('price', 0.0)):.2f}",
                                        'available': not opt.get('sold_out', True)
                                    })

                                # Ensure at least one default variant
                                if not product_item['variants']:
                                    product_item['variants'].append({
                                        'id': str(product.get('id')),
                                        'title': 'Default',
                                        'price': product_item['price'],
                                        'sold_out': not product.get('on_sale', False)
                                    })

                                items.append(product_item)

                            except Exception as e:
                                logger.error(f"Error processing product {product.get('id', 'unknown')}: {e}")
                                continue

                        page += 1

                    except json.JSONDecodeError as e:
                        logger.error(f"JSON decode error for {api_url}: {e}")
                        break

                except rq.exceptions.HTTPError as e:
                    logger.error(f"HTTP error for {api_url}: {e}")
                    if e.response and e.response.status_code == 404:
                        logger.warning(f"Store {url} might not exist or products.json is not available")
                    break

                except rq.exceptions.RequestException as e:
                    logger.error(f'Error scraping page {page} of {url}: {str(e)}')
                    break

            logger.info(f'Finished fetching products for {url} ({products_fetched} total processed)')

        return items

    def _checker(self, product_id, current_data):
        """Determines whether the product status has changed."""
        existing = next((item for item in self.instock if item['id'] == product_id), None)
        if existing:
            # Check for changes in price, status, or title
            return (
                existing.get('price') == current_data.get('price') and
                existing.get('status') == current_data.get('status') and
                existing.get('title') == current_data.get('title')
            )
        return False # New product

    def _discord_webhook(self, product_item, base_url, website_name):
        """Send a webhook to Discord with product details."""
        if product_item.get('initial'):
            logging.info("Sending initial webhook...")
            # Potentially send a startup message here if desired
            return

        logging.info(f"Sending webhook for: {product_item.get('title')}")
        current_time_utc = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

        embed_data = {
            "username": self.config['USERNAME'],
            "avatar_url": self.config['AVATAR_URL'],
            "embeds": [{
                "title": product_item['title'],
                "url": product_item['url'],
                "color": int(self.config['COLOUR'], 16),
                "thumbnail": {"url": product_item['image']},
                "fields": [],
                "footer": {
                    'text': f"{self.config['USERNAME']} • {current_time_utc}",
                    'icon_url': self.config['AVATAR_URL']
                },
                "author": {
                    "name": website_name,
                    "icon_url": self.config['STORE_ICON_URL']
                }
            }]
        }

        is_available = any(v.get('available', False) for v in product_item.get('variants', []))
        status_value = "In-Stock" if is_available else "Sold Out"

        embed_data["embeds"][0]["fields"].append({"name": "Status", "value": status_value, "inline": False})
        embed_data["embeds"][0]["fields"].append({"name": "Price", "value": f"${product_item['price']}", "inline": True})
        embed_data["embeds"][0]["fields"].append({"name": "SKU/Handle", "value": product_item['handle'], "inline": True})


        for variant in product_item.get('variants', []):
            variant_id = variant.get('id', product_item.get('id')) # Fallback to product ID
            variant_title = variant.get('title', 'Default')
            variant_status = "Available" if variant.get('available') else "Sold Out"
            atc_link = f"{base_url.replace('/products.json', '')}/cart/add_item?item_id={variant_id}" # Example ATC, may vary
            embed_data["embeds"][0]["fields"].append({
                "name": f"Variant: {variant_title}",
                "value": f"ID: {variant_id} - Status: {variant_status}\n[Add to Cart]({atc_link})", # Basic ATC
                "inline": False
            })

        ebay_title_query = quote_plus(product_item['title'])
        ebay_referral = "&campid=YOUR_CAMPID&toolid=10001" # Placeholder for actual referral
        eBay_sold_link = f"https://www.ebay.com/sch/i.html?_nkw={ebay_title_query}&LH_Sold=1&LH_Complete=1{ebay_referral}"
        eBay_active_link = f"https://www.ebay.com/sch/i.html?_nkw={ebay_title_query}{ebay_referral}"

        embed_data["embeds"][0]["fields"].extend([
            {"name": "eBay [US]", "value": f"[Sold]({eBay_sold_link}) | [Active]({eBay_active_link})", "inline": True},
            {"name": "eBay [UK]", "value": f"[Sold]({eBay_sold_link.replace('www.ebay.com', 'www.ebay.co.uk')}) | [Active]({eBay_active_link.replace('www.ebay.com', 'www.ebay.co.uk')})", "inline": True},
        ])

        try:
            logging.info(f"Sending webhook to: {self.config['WEBHOOK']}")
            result = self.session.post(self.config['WEBHOOK'], data=json.dumps(embed_data), headers={"Content-Type": "application/json"})
            result.raise_for_status()
            logging.info(f"Webhook delivered successfully: {result.status_code} for {product_item.get('title')}")
            time.sleep(1)
        except rq.exceptions.RequestException as e:
            logging.error(f'Failed to send webhook for {product_item.get("title")}: {str(e)}')
            if hasattr(e, 'response') and e.response is not None:
                logging.error(f"Webhook response: {e.response.text}")


    def _comparitor(self, product, website_name):
        """Compare and send notifications for new or restocked products."""
        if product['type'] != 'product':
            return

        title = product.get('title', 'Unknown Product')
        product_id = product.get('id')

        # ---- START DEBUG PRINTS ----
        if product_id == "30476152": # Only print for your specific test item
            print(f"DEBUG: _comparitor called for product ID: {product_id}, Title: {title}")
        # ---- END DEBUG PRINTS ----

        logging.info(f"Checking availability for: {title} (ID: {product_id})")

        try:
            is_in_stock_now = any(variant.get('available', False) for variant in product.get('variants', []))

            # ---- START DEBUG PRINTS ----
            if product_id == "30476152":
                print(f"DEBUG: For ID {product_id}, is_in_stock_now: {is_in_stock_now}")
            # ---- END DEBUG PRINTS ----

            product_data_for_tracking = {
                'id': product_id,
                'title': title,
                'price': product.get('price'),
                'url': product.get('url'),
                'status': 'available' if is_in_stock_now else 'sold-out',
                'images': product.get('images', []), # Keep all images for potential future use
                'handle': product.get('handle', '')
                # Storing full variant data in self.instock might be too much,
                # but could be useful for detailed change tracking.
                # For now, stick to top-level status.
            }

            existing_idx = next((i for i, item in enumerate(self.instock) if item['id'] == product_id), -1)

            # ---- START DEBUG PRINTS ----
            if product_id == "30476152":
                print(f"DEBUG: For ID {product_id}, existing_idx in self.instock: {existing_idx}")
                if existing_idx == -1:
                    print(f"DEBUG: ID {product_id} is considered NEW by _comparitor.")
                else:
                    print(f"DEBUG: ID {product_id} is considered EXISTING by _comparitor (index: {existing_idx}).")
            # ---- END DEBUG PRINTS ----

            if existing_idx == -1:  # New product
                logging.info(f"New product found: '{title}' (Status: {product_data_for_tracking['status']})")
                print(f"DEBUG_COMPARITOR_ADD: Adding NEW product to self.instock: ID {product_id}, Title: {title}") # ADD THIS LINE
                self.instock.append(product_data_for_tracking)
                # ---- START DEBUG PRINTS ----
                if product_id == "30476152":
                    print(f"DEBUG: For NEW ID {product_id}, attempting to notify if is_in_stock_now ({is_in_stock_now}) is True.")
                # ---- END DEBUG PRINTS ----
                if is_in_stock_now:
                    # ---- START DEBUG PRINTS ----
                    if product_id == "30476152":
                         print(f"DEBUG: ID {product_id} IS IN STOCK (is_in_stock_now={is_in_stock_now}). ATTEMPTING WEBHOOK.")
                    # ---- END DEBUG PRINTS ----
                    self._discord_webhook(product, self.config['URL'], website_name)
                    logging.info(f'Notification sent for new product: {title}')
            else: # Existing product, check for changes
                is_unchanged = self._checker(product_id, product_data_for_tracking)
                old_status = self.instock[existing_idx]['status']
                new_status = product_data_for_tracking['status']

                if not is_unchanged:
                    log_message = f"Product '{title}' details changed."
                    if old_status != new_status:
                        log_message += f" Status: {old_status} -> {new_status}."
                    else: # Price or other details changed
                        log_message += f" Price: {self.instock[existing_idx].get('price')} -> {product_data_for_tracking.get('price')}."
                    logging.info(log_message)

                    self.instock[existing_idx] = product_data_for_tracking
                    if is_in_stock_now : # Notify if it's now in stock, or if it was in stock and details changed
                        self._discord_webhook(product, self.config['URL'], website_name)
                        logging.info(f'Notification sent for updated product: {title}')
                else:
                    logging.debug(f"Product '{title}' unchanged.")


            logging.debug(f"Currently tracking {len(self.instock)} products")

        except Exception as e:
            logging.error(f"Error in _comparitor for {title}: {str(e)}", exc_info=True)


    def run(self):
        """Main function to monitor and send notifications."""
        logging.info('Monitor starting.')
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning) # Moved here

        self._load_instock()
        # ---- START NEW DEBUG PRINT ----
        print(f"DEBUG: After _load_instock(), is ID 30476152 in self.instock? {'Yes' if any(item['id'] == '30476152' for item in self.instock) else 'No'}")
        print(f"DEBUG: Length of self.instock after load: {len(self.instock)}")
        # ---- END NEW DEBUG PRINT ----
        is_first_run = not self.instock # Simpler check

        if is_first_run:
            logging.info("First run detected - will initialize product tracking without immediate notifications for existing items.")

        if not self.config['URL']:
            logging.error('No URL provided. Please set the URL in .env file.')
            return

        base_url = self.config['URL'].rstrip('/')
        urls = [base_url] # Assuming single URL monitoring for now

        if not self._check_bigcartel_url(urls):
            logging.error('Invalid URL format. Please provide a valid BigCartel store URL.')
            return

        parsed_url = urlparse(base_url)
        website_name = parsed_url.netloc if parsed_url.netloc else base_url
        self._discord_webhook({'initial': True, 'title': 'Monitor Started'}, base_url, website_name) # Send a startup notification

        proxy_no = 0
        proxy_list = [p.strip() for p in self.config['PROXY'].split(',') if p.strip()]
        keywords = [k.strip().lower() for k in self.config['KEYWORDS'].split(',') if k.strip()]


        logging.info("Monitor Configuration:")
        logging.info(f"Store URL: {self.config['URL']}")
        logging.info(f"Check Delay: {self.config['DELAY']} seconds")
        logging.info(f"Keywords Filter: {self.config['KEYWORDS'] or 'None'}")
        logging.info(f"Proxies configured: {len(proxy_list)}")


        current_proxy = self._get_proxy(proxy_list, proxy_no)
        logging.info(f"Starting with proxy: {current_proxy.get('http', 'No proxy')} at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        while True:
            current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logging.info(f"Monitoring for new products... [{current_time_str}]")
            headers = {'User-Agent': self.user_agent_rotator.get_random_user_agent()}

            try:
                items = self._scrape_site(urls, headers, current_proxy)
                if items is None: # Indicates a critical error in scraping
                    logging.error("Scraping returned None, attempting proxy rotation.")
                    raise Exception("Scraping failed critically")


                for item in items: # 5th indent level (20 spaces)
                    title_lower = item['title'].lower()
                    if not keywords or any(key in title_lower for key in keywords): # 6th indent level (24 spaces)
                        if is_first_run: # 7th indent level (28 spaces)
                            # On first run, just add to instock without notification
                            is_in_stock_now = any(variant.get('available', False) for variant in item.get('variants', []))
                            self.instock.append({
                                'id': item['id'],
                                'title': item['title'],
                                'price': item['price'],
                                'url': item['url'],
                                'status': 'available' if is_in_stock_now else 'sold-out',
                                'images': item.get('images', []),
                                'handle': item['handle']
                            })
                        else: # This else corresponds to `if is_first_run:` (7th indent level - 28 spaces)
                            # ---- START NEW DEBUG PRINT ----
                            if item.get('id') == "30476152": # 8th indent level (32 spaces)
                                print(f"DEBUG RUN_LOOP: Before _comparitor for ID 30476152.")
                                print(f"DEBUG RUN_LOOP: is_first_run is: {is_first_run}")
                                print(f"DEBUG RUN_LOOP: Is ID 30476152 in self.instock NOW? {'Yes' if any(i['id'] == '30476152' for i in self.instock) else 'No'}")
                                if any(i['id'] == '30476152' for i in self.instock):
                                    found_at = next((idx for idx, i_s in enumerate(self.instock) if i_s['id'] == '30476152'), -99)
                                    print(f"DEBUG RUN_LOOP: If yes, found at index: {found_at}")
                            # ---- END NEW DEBUG PRINT ----
                            self._comparitor(item, website_name) # 8th indent level (32 spaces)

                if is_first_run and items: # 5th indent level (20 spaces)
                    logging.info(f"Initialized tracking for {len(self.instock)} products.")
                    is_first_run = False # Mark first run as complete

                self._save_instock() # 5th indent level (20 spaces)

            except Exception as e: # 4th indent level (16 spaces)
                logging.error('Error during monitoring loop: %s', str(e), exc_info=True)
                logging.info("Attempting to rotate proxy and user-agent.")

                if proxy_list: # Only rotate if proxies are configured
                    proxy_no = (proxy_no + 1) % len(proxy_list)
                current_proxy = self._get_proxy(proxy_list, proxy_no) # Will fetch free if proxy_list is empty or exhausted
                logging.info(f"Switched to new proxy: {current_proxy.get('http', 'No proxy')} at {current_time_str}")

            logging.info(f"Waiting for {self.config['DELAY']} seconds before next check.")
            time.sleep(self.config['DELAY'])

if __name__ == '__main__':
    monitor_instance = BigCartelMonitor()
    monitor_instance.run()
