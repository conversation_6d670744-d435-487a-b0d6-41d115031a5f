# 🐳 BigCartel Monitor - Docker Deployment Guide

## 📦 Project Structure (Docker-Ready)

```
bigcartel_monitor/
├── monitor_improved.py      # 🚀 Enhanced monitor application
├── .env                     # 🔧 Environment configuration
├── requirements.txt         # 📋 Python dependencies (optimized)
├── Dockerfile              # 🐳 Production Docker image
├── docker-compose.yml      # 🔧 Docker Compose configuration
├── .dockerignore           # 🚫 Docker build exclusions
├── README.md               # 📖 Project documentation
├── data/                   # 💾 Persistent data directory
└── logs/                   # 📝 Log files directory
```

## 🚀 Quick Start

### **1. Build and Run with Docker Compose (Recommended)**

```bash
# Build and start the monitor
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the monitor
docker-compose down
```

### **2. Build and Run with Docker**

```bash
# Build the image
docker build -t bigcartel-monitor .

# Run the container
docker run -d \
  --name squindo-monitor \
  --restart unless-stopped \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  bigcartel-monitor

# View logs
docker logs -f squindo-monitor

# Stop the container
docker stop squindo-monitor
```

## 🔧 Configuration

### **Environment Variables (.env file)**

```bash
# Required
URL=https://squindo.bigcartel.com
WEBHOOK=https://discord.com/api/webhooks/your-webhook

# Optional
DELAY=15
LOCATION=US
KEYWORDS=
PROXY=
USERNAME=Squindo Monitor
AVATAR_URL=https://media.discordapp.net/attachments/...
COLOUR=00FF00
STORE_ICON_URL=https://media.discordapp.net/attachments/...
```

## 🐳 Docker Features

### **Production Optimizations**
- ✅ **Alpine Linux base** - Minimal image size (~50MB)
- ✅ **Non-root user** - Enhanced security
- ✅ **Health checks** - Container monitoring
- ✅ **Resource limits** - Memory and CPU constraints
- ✅ **Log rotation** - Prevents disk space issues
- ✅ **SSL certificates** - Proper HTTPS handling

### **Data Persistence**
- 📁 **`/app/data`** - Product tracking data
- 📝 **`/app/logs`** - Application logs
- 🔄 **Auto-restart** - Container restarts on failure

### **Monitoring & Health**
- 🏥 **Health checks** - Every 30 seconds
- 📊 **Resource monitoring** - Memory and CPU usage
- 📝 **Structured logging** - JSON format with rotation

## 📋 Docker Commands

### **Container Management**
```bash
# View running containers
docker ps

# View all containers
docker ps -a

# View container logs
docker logs squindo-monitor

# Follow logs in real-time
docker logs -f squindo-monitor

# Execute command in container
docker exec -it squindo-monitor sh

# Restart container
docker restart squindo-monitor

# Remove container
docker rm squindo-monitor
```

### **Image Management**
```bash
# List images
docker images

# Remove image
docker rmi bigcartel-monitor

# Build with no cache
docker build --no-cache -t bigcartel-monitor .

# Tag for registry
docker tag bigcartel-monitor your-registry/bigcartel-monitor:latest
```

### **Data Management**
```bash
# Backup tracking data
docker cp squindo-monitor:/app/data ./backup-data

# Restore tracking data
docker cp ./backup-data squindo-monitor:/app/data

# View container filesystem
docker exec -it squindo-monitor ls -la /app
```

## 🔍 Troubleshooting

### **Common Issues**

**Container won't start:**
```bash
# Check container status
docker ps -a

# View container logs
docker logs squindo-monitor

# Check environment variables
docker exec squindo-monitor env
```

**SSL/Connection errors:**
```bash
# Test network connectivity
docker exec squindo-monitor ping google.com

# Check SSL certificates
docker exec squindo-monitor ls -la /etc/ssl/certs/
```

**Permission issues:**
```bash
# Check file permissions
docker exec squindo-monitor ls -la /app

# Fix data directory permissions
sudo chown -R 1000:1000 ./data ./logs
```

## 📊 Monitoring

### **Health Check Status**
```bash
# View health status
docker inspect squindo-monitor | grep Health -A 10

# Manual health check
docker exec squindo-monitor python -c "import requests; requests.get('https://httpbin.org/status/200', timeout=5)"
```

### **Resource Usage**
```bash
# View resource usage
docker stats squindo-monitor

# View detailed container info
docker inspect squindo-monitor
```

## 🚀 Production Deployment

### **Docker Swarm**
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml bigcartel-stack

# View services
docker service ls

# Scale service
docker service scale bigcartel-stack_bigcartel-monitor=2
```

### **Kubernetes**
```bash
# Create deployment
kubectl create deployment bigcartel-monitor --image=bigcartel-monitor

# Expose service
kubectl expose deployment bigcartel-monitor --port=80

# View pods
kubectl get pods

# View logs
kubectl logs -f deployment/bigcartel-monitor
```

## 🔒 Security Best Practices

- ✅ **Non-root user** - Container runs as `monitor` user
- ✅ **Read-only filesystem** - Application files are immutable
- ✅ **Resource limits** - Prevents resource exhaustion
- ✅ **Health checks** - Automatic failure detection
- ✅ **SSL verification** - Secure HTTPS connections
- ✅ **Environment isolation** - Containerized execution

## 📈 Performance

### **Resource Requirements**
- **Memory**: 128MB minimum, 256MB limit
- **CPU**: 0.1 core minimum, 0.5 core limit
- **Disk**: ~100MB for image, minimal for data
- **Network**: HTTPS outbound for API calls

### **Optimization Tips**
- Use Docker Compose for easier management
- Mount volumes for data persistence
- Configure log rotation to prevent disk issues
- Monitor resource usage with `docker stats`
- Use health checks for automatic recovery

**Your BigCartel monitor is now Docker-ready for production deployment! 🚀**
