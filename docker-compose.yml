version: '3.8'

services:
  bigcartel-monitor:
    build: .
    container_name: squindo-monitor
    restart: unless-stopped
    
    # Environment variables (override .env file)
    environment:
      - URL=${URL}
      - WEBHOOK=${WEBHOOK}
      - DELAY=${DELAY:-15}
      - LOCATION=${LOCATION:-US}
      - KEYWORDS=${KEYWORDS:-}
      - PROXY=${PROXY:-}
      - USERNAME=${USERNAME:-Squindo Monitor}
      - AVATAR_URL=${AVATAR_URL}
      - COLOUR=${COLOUR:-00FF00}
      - STORE_ICON_URL=${STORE_ICON_URL}
    
    # Mount volume for persistent data
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('https://httpbin.org/status/200', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Create named volumes for data persistence
volumes:
  monitor-data:
    driver: local
  monitor-logs:
    driver: local
