#!/usr/bin/env python3
"""
Quick test script to verify the improved BigCartel monitor is working correctly.
"""

import sys
import os
from dotenv import load_dotenv

def test_monitor():
    """Test the monitor initialization and configuration."""
    print("🧪 BIGCARTEL MONITOR - QUICK TEST")
    print("=" * 50)
    
    try:
        # Load environment variables
        load_dotenv()
        print("✅ Environment variables loaded")
        
        # Test imports
        from monitor_improved import BigCartelMonitor, MonitorConfig, Product, ProductVariant
        print("✅ All imports successful")
        
        # Test configuration
        url = os.getenv('URL')
        webhook = os.getenv('WEBHOOK')
        
        if not url or not webhook:
            print("❌ Missing required environment variables (URL or WEBHOOK)")
            return False
            
        print(f"✅ Configuration found:")
        print(f"   URL: {url}")
        print(f"   Webhook: {webhook[:50]}...")
        
        # Test monitor initialization
        monitor = BigCartelMonitor()
        print("✅ Monitor initialized successfully")
        
        # Test configuration validation
        print(f"✅ Monitor configuration:")
        print(f"   Store: {monitor.config.url}")
        print(f"   Username: {monitor.config.username}")
        print(f"   Delay: {monitor.config.delay} seconds")
        print(f"   Keywords: {monitor.config.keywords or 'All products'}")
        
        # Test URL validation
        urls = [monitor.config.url]
        if monitor._is_valid_bigcartel_url(urls):
            print("✅ BigCartel URL validation passed")
        else:
            print("❌ BigCartel URL validation failed")
            return False
        
        # Test data classes
        variant = ProductVariant(id="1", title="Test", price=10.0, available=True)
        product = Product(
            id="123",
            title="Test Product",
            handle="test",
            price=10.0,
            url="https://test.com",
            image=None,
            images=[],
            variants=[variant],
            status="available"
        )
        print("✅ Data classes working correctly")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("Your improved BigCartel monitor is ready to use!")
        print("\nTo start monitoring:")
        print("python3 monitor_improved.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_monitor()
    sys.exit(0 if success else 1)
