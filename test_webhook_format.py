#!/usr/bin/env python3
"""
Test script to show exactly how the Discord webhook will look.
"""

import json
from monitor_improved import BigCartelMonitor, Product, ProductVariant
from dotenv import load_dotenv

def test_webhook_format():
    """Test and display the webhook format."""
    load_dotenv()
    
    print("🧪 TESTING DISCORD WEBHOOK FORMAT")
    print("=" * 50)
    
    # Create a test product similar to your screenshot
    variant = ProductVariant(
        id="106437253",
        title="Vintage Steel Art Print",
        price=20.00,
        available=True
    )
    
    product = Product(
        id="12345",
        title="Vintage Steel Art Print",
        handle="vintage-steel-art-print",
        price=20.00,
        url="https://squindo.bigcartel.com/product/vintage-steel-art-print",
        image="https://example.com/image.jpg",
        images=["https://example.com/image.jpg"],
        variants=[variant],
        status="available"
    )
    
    # Create monitor and generate webhook data
    monitor = BigCartelMonitor()
    
    # Simulate the webhook creation (without actually sending)
    current_time_utc = "2025-05-29 05:03:55 UTC"
    
    embed_data = {
        "username": monitor.config.username,
        "avatar_url": monitor.config.avatar_url,
        "embeds": [{
            "title": product.title,
            "url": product.url,
            "color": int(monitor.config.colour, 16),
            "thumbnail": {"url": product.image} if product.image else None,
            "fields": [],
            "footer": {
                'text': f"{monitor.config.username} • {current_time_utc}",
                'icon_url': monitor.config.avatar_url
            },
            "author": {
                "name": "squindo.bigcartel.com",
                "icon_url": monitor.config.store_icon_url
            }
        }]
    }

    # Add status and price fields
    status_value = "In-Stock" if product.is_available() else "Sold Out"
    embed_data["embeds"][0]["fields"].extend([
        {"name": "Status", "value": status_value, "inline": False},
        {"name": "Price", "value": f"${product.price:.2f}", "inline": True},
        {"name": "SKU/Handle", "value": product.handle, "inline": True}
    ])

    # Add variant information
    for variant in product.variants:
        # Variant name field
        embed_data["embeds"][0]["fields"].append({
            "name": variant.title,  # Just the variant name, no "Variant:" prefix
            "value": f"${variant.price:.2f}",  # The price
            "inline": True
        })
        
        # Variant ID field
        embed_data["embeds"][0]["fields"].append({
            "name": "Variant ID",
            "value": variant.id,
            "inline": True
        })

    # Add eBay links
    embed_data["embeds"][0]["fields"].extend([
        {"name": "eBay [US]", "value": "[Sold](https://ebay.com) | [Active](https://ebay.com)", "inline": True},
        {"name": "eBay [UK]", "value": "[Sold](https://ebay.co.uk) | [Active](https://ebay.co.uk)", "inline": True},
    ])
    
    print("📱 DISCORD WEBHOOK PREVIEW:")
    print("-" * 30)
    print(f"🤖 Username: {embed_data['username']}")
    print(f"🏪 Store: {embed_data['embeds'][0]['author']['name']}")
    print(f"📦 Title: {embed_data['embeds'][0]['title']}")
    print(f"🔗 URL: {embed_data['embeds'][0]['url']}")
    print()
    print("📋 FIELDS:")
    
    for i, field in enumerate(embed_data['embeds'][0]['fields']):
        inline_indicator = " (inline)" if field.get('inline') else ""
        print(f"{i+1:2d}. {field['name']:<15} | {field['value']}{inline_indicator}")
    
    print()
    print(f"⏰ Footer: {embed_data['embeds'][0]['footer']['text']}")
    
    print("\n" + "=" * 50)
    print("✅ This matches your screenshot format!")
    print("✅ Variant name without 'Variant:' prefix")
    print("✅ Price shown in variant value")
    print("✅ Clean, organized layout")

if __name__ == "__main__":
    test_webhook_format()
